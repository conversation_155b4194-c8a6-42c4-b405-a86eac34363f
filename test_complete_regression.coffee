# 完整回归测试 - 包含所有已知问题的测试用例
propAddress = require './src/lib/propAddress'

# 所有测试用例
allTestCases = [
  # 基础功能测试
  {
    input: ['123 Main St, Toronto, ON', 'Toronto']
    expected: '123 Main St'
    description: '基本地址提取'
  }
  {
    input: ['Unit 5, 456 Oak Ave, Vancouver, BC', 'Vancouver']
    expected: '456 Oak Ave'
    description: 'Unit信息过滤'
  }
  {
    input: ['789 Pine Rd, North York, Toronto, ON', 'Toronto']
    expected: '789 Pine Rd, North York'
    description: '复合城市名称处理'
  }
  {
    input: ['321 Elm St, ON M5V 3A8', 'Toronto']
    expected: '321 Elm St'
    description: '省份缩写识别'
  }
  {
    input: ['654 Maple Dr', null]
    expected: '654 Maple Dr'
    description: '无逗号地址'
  }
  {
    input: ['', 'Toronto']
    expected: ''
    description: '空地址处理'
  }
  {
    input: [null, 'Toronto']
    expected: null
    description: 'null输入处理'
  }
  
  # 第一批城市匹配问题
  {
    input: ['658 Chillon Street, Alfred & Plantagenet, ON K0A 3K0', 'Alfred and Plantagenet']
    expected: '658 Chillon Street'
    description: '& vs and 符号差异'
  }
  
  # 新发现的问题
  {
    input: ['29 Boland Street, Killaloe Hagarty & Richards, ON K0J 2A0', 'Killaloe, Hagarty And Richards']
    expected: '29 Boland Street'
    description: '逗号和&/and组合差异'
  }
  
  # 其他可能的边界情况
  {
    input: ['100 Test Ave, City A & B, Province', 'City A and B']
    expected: '100 Test Ave'
    description: '复杂逗号和符号组合'
  }
  {
    input: ['200 Sample St, North & South Bay, BC', 'North and South Bay']
    expected: '200 Sample St'
    description: '多词城市名称符号差异'
  }
]

console.log '运行完整回归测试...\n'

passedTests = 0
failedTests = []

for testCase, index in allTestCases
  [origAddr, city] = testCase.input
  result = propAddress.extractAddressFromOrigAddr(origAddr, city)
  
  if result == testCase.expected
    console.log "✓ 测试 #{index + 1}: #{testCase.description} - 通过"
    passedTests++
  else
    console.log "✗ 测试 #{index + 1}: #{testCase.description} - 失败"
    console.log "  输入: origAddr='#{origAddr}', city='#{city}'"
    console.log "  期望: '#{testCase.expected}'"
    console.log "  实际: '#{result}'"
    failedTests.push(testCase)

console.log "\n测试结果: #{passedTests}/#{allTestCases.length} 通过"

if failedTests.length == 0
  console.log '🎉 所有测试通过！城市匹配逗号处理修复成功。'
else
  console.log '⚠️  部分测试失败，需要进一步调试。'
  console.log '失败的测试:'
  for test in failedTests
    console.log "- #{test.description}"
